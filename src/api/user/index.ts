import {
  apiHost,
  fetch,
} from '@mu/business-basic';
import { UserInfo } from '@src/types/user';
import { userInfoTranslator, avatarTranslator } from './translator';

/**
 * 用户相关 API 接口 - DDD 数据接口层
 *
 * 数据接口层是整个项目的根基，位于架构分层中的基础设施层。
 * 提供了结构清晰、定义规范、前端可直接使用的数据。
 *
 * 职责范围：
 * 1. 负责 HTTP 请求，是项目中唯一与后端服务进行交互的一层
 * 2. 统一管理所有用户相关的接口函数
 * 3. 配合 translator 层进行数据转换
 * 4. 只能被领域层调用，收敛业务逻辑
 *
 * 设计原则：
 * - 单一职责：只负责数据请求，不包含业务逻辑
 * - 数据转换：配合 translator 将后端数据转换为前端可用格式
 * - 统一管理：避免接口分散到各个页面
 */

/**
 * 获取用户基本信息
 *
 * 调用后端接口获取用户的基本信息，包括：
 * - 用户ID、客户ID
 * - 昵称、手机号
 * - 登录状态、实名状态
 * - 用户标签等
 *
 * @returns 经过转换的用户信息对象
 * @throws 当网络请求失败时抛出异常
 */
async function getUserInfo(): Promise<Partial<UserInfo>> {
  const response = await fetch(
    `${apiHost.mgp}?operationId=mucfc.user.infoMaintain.getCustInfo`, {
      autoLoading: false,
      autoToast: false,
    },
  );
  return userInfoTranslator(response);
}

/**
 * 获取用户头像
 *
 * 根据头像ID获取用户头像的base64数据。
 * 如果没有提供头像ID，则返回空对象。
 *
 * @param avatarId 头像ID，可选参数
 * @returns 包含头像数据的对象，如果没有头像ID则返回空对象
 * @throws 当网络请求失败时抛出异常
 *
 * @example
 * ```typescript
 * // 获取指定头像
 * const avatarData = await getAvatar('avatar123');
 * console.log(avatarData.avatar); // base64 头像数据
 *
 * // 没有头像ID
 * const emptyData = await getAvatar(); // 返回 {}
 * ```
 */
async function getAvatar(avatarId?: string): Promise<Partial<Pick<UserInfo, 'avatar'>>> {
  let user: Partial<Pick<UserInfo, 'avatar'>> | undefined;

  if (avatarId) {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.infoMaintain.getHeadImageContent`, {
        data: {
          headImageId: avatarId,
        },
        autoLoading: false,
        autoToast: false,
      },
    );
    user = avatarTranslator(response);
  }

  return user || {};
}

export {
  getAvatar,
  getUserInfo,
};
